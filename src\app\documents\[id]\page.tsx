"use client";

import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { AlertCircle, ArrowLeft, Bell, Shield } from "lucide-react";
import { useNotifications } from "@/contexts/notification-context";
import type { Notification } from "@/contexts/notification-context";
import { PDFImageViewer } from "@/components/pdf-image-viewer";

export default function DocumentViewerPage() {
  const params = useParams();
  const router = useRouter();
  const { notifications, markAsRead, isAdminMode } = useNotifications();
  const [notification, setNotification] = useState<Notification | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);
  const [adminModeChecked, setAdminModeChecked] = useState(false);

  useEffect(() => {
    const checkAdminAndLoadNotification = async () => {
      // Set hydrated to true on client side
      setIsHydrated(true);
      setIsLoading(false);

      console.log("Document viewer - Admin mode:", isAdminMode);
      console.log(
        "Document viewer - Notifications count:",
        notifications.length
      );

      // Check if user has admin access
      if (!isAdminMode) {
        console.log("Document viewer - No admin access, returning");
        return;
      }

      const notificationId = params.id as string;

      // Try to find in local state first
      let foundNotification = notifications.find(
        (n) => n.id === notificationId
      );

      // If not found locally, fetch from database
      if (!foundNotification) {
        try {
          const response = await fetch(`/api/notifications/${notificationId}`);
          const data = await response.json();
          if (data.success) {
            foundNotification = {
              ...data.notification,
              createdAt: new Date(data.notification.createdAt),
            };
          }
        } catch (error) {
          console.error("Error fetching notification:", error);
        }
      }

      if (foundNotification) {
        console.log("Found notification:", foundNotification);
        setNotification(foundNotification);

        // Mark as read when viewing details
        if (!foundNotification.isRead) {
          markAsRead(foundNotification.id);
        }

        // Log PDF information if it exists
        if (foundNotification.pdfUrl) {
          console.log("PDF URL:", foundNotification.pdfUrl);
          console.log("PDF File Path:", foundNotification.pdfFilePath);
          console.log("PDF File Name:", foundNotification.pdfFileName);
        } else {
          console.log("No PDF URL found in notification");
        }
      }
    };

    checkAdminAndLoadNotification();
  }, [params.id, notifications, markAsRead, isAdminMode]);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Separate effect to handle admin mode checking with delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setAdminModeChecked(true);
      console.log("Document viewer - Admin mode check completed:", isAdminMode);
    }, 100); // Small delay to ensure admin mode is loaded

    return () => clearTimeout(timer);
  }, [isAdminMode]);

  if (!isHydrated || !adminModeChecked) {
    return null; // Prevent hydration mismatch and wait for admin mode check
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading document...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not in admin mode
  if (!isAdminMode) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Admin Access Required</h3>
          <p className="text-muted-foreground mb-6">
            You need to enable admin mode to view documents.
          </p>
          <Button onClick={() => router.push("/settings")}>
            <Shield className="h-4 w-4 mr-2" />
            Go to Settings
          </Button>
        </div>
      </div>
    );
  }

  if (!notification) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">Document Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The document you're looking for doesn't exist or has been removed.
          </p>
          <Button onClick={() => router.push("/admin")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Admin
          </Button>
        </div>
      </div>
    );
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "success":
        return <Bell className="h-5 w-5 text-green-600" />;
      case "warning":
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Shield className="h-5 w-5 text-blue-600" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "success":
        return "border-green-200 bg-green-50";
      case "warning":
        return "border-yellow-200 bg-yellow-50";
      case "error":
        return "border-red-200 bg-red-50";
      default:
        return "border-blue-200 bg-blue-50";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/admin")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Admin
              </Button>
              <div className="h-6 w-px bg-border" />
              <h1 className="text-xl font-semibold">Document Viewer</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Document Info Panel */}
          <div className="lg:col-span-1 space-y-6">
            {/* Notification Details */}
            <div
              className={`p-4 rounded-lg border ${getTypeColor(
                notification.type
              )}`}
            >
              <div className="flex items-start space-x-3">
                {getTypeIcon(notification.type)}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm mb-1">
                    {notification.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    {notification.message}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(notification.createdAt).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            {/* PDF Information */}
            {notification.pdfFileName && (
              <div className="bg-card p-4 rounded-lg border">
                <h4 className="font-semibold mb-3">Document Information</h4>
                <div className="space-y-2">
                  <div>
                    <Label className="text-xs text-muted-foreground">
                      File Name
                    </Label>
                    <p className="text-sm font-medium break-all">
                      {notification.pdfFileName}
                    </p>
                  </div>
                  {notification.pdfFilePath && (
                    <div>
                      <Label className="text-xs text-muted-foreground">
                        File Path
                      </Label>
                      <p className="text-xs text-muted-foreground font-mono break-all">
                        {notification.pdfFilePath}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* PDF Data Information */}
            {notification.pdfData && (
              <div className="bg-card p-4 rounded-lg border">
                <h4 className="font-semibold mb-3">Embedded Data</h4>
                <div className="space-y-2">
                  <div>
                    <Label className="text-xs text-muted-foreground">
                      Template
                    </Label>
                    <p className="text-sm">
                      {notification.pdfData.templateName}
                    </p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">
                      Generated
                    </Label>
                    <p className="text-sm">
                      {new Date(
                        notification.pdfData.generatedAt
                      ).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">
                      Layout
                    </Label>
                    <p className="text-sm">{notification.pdfData.layoutSize}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* PDF Viewer */}
          <div className="lg:col-span-2">
            {notification.pdfUrl ? (
              <PDFImageViewer
                pdfUrl={notification.pdfUrl}
                fileName={notification.pdfFileName}
                className="h-full border rounded-lg"
              />
            ) : (
              <div className="h-full border rounded-lg flex items-center justify-center bg-muted/30">
                <div className="text-center">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    No Document Available
                  </h3>
                  <p className="text-muted-foreground">
                    This notification doesn't have an associated PDF document.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
