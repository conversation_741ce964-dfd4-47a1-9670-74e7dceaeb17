import { NextRequest, NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import path from 'path';

// For server-side PDF processing, we'll return the PDF data and let the client handle image conversion

// Optimized function to search for embedded data without converting entire buffer to string
function findEmbeddedData(buffer: Buffer): { hasEmbeddedData: boolean; data: any } {
  const startMarker = Buffer.from('LDIS_DATA_BEGIN:', 'utf8');
  const endMarker = Buffer.from(':LDIS_DATA_END', 'utf8');

  const startIndex = buffer.indexOf(startMarker);
  if (startIndex === -1) {
    return { hasEmbeddedData: false, data: null };
  }

  const endIndex = buffer.indexOf(endMarker, startIndex);
  if (endIndex === -1) {
    return { hasEmbeddedData: false, data: null };
  }

  try {
    // Extract only the JSON portion
    const jsonStart = startIndex + startMarker.length;
    const jsonBuffer = buffer.subarray(jsonStart, endIndex);
    const jsonData = jsonBuffer.toString('utf8');
    const parsedData = JSON.parse(jsonData);

    return { hasEmbeddedData: true, data: parsedData };
  } catch (parseError) {
    console.error('Error parsing embedded JSON:', parseError);
    return { hasEmbeddedData: false, data: null };
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'PDF file is required' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Save PDF file to public/uploads/pdfs folder
    const timestamp = Date.now();
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_'); // Sanitize filename
    const fileName = `${timestamp}-${sanitizedFileName}`;
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'pdfs');
    const filePath = path.join(uploadsDir, fileName);

    // Ensure uploads directory exists
    try {
      const { mkdir } = await import('fs/promises');
      const { existsSync } = await import('fs');

      if (!existsSync(uploadsDir)) {
        await mkdir(uploadsDir, { recursive: true });
      }

      // Save file synchronously to ensure it's saved before response
      await writeFile(filePath, buffer);
    } catch (saveError) {
      console.error('Error saving PDF file:', saveError);
      return NextResponse.json(
        { error: 'Failed to save PDF file' },
        { status: 500 }
      );
    }

    // Optimized embedded data search
    const { hasEmbeddedData, data: parsedData } = findEmbeddedData(buffer);

    // Return response immediately without base64 conversion
    // The client can create object URL from the original file if needed
    return NextResponse.json({
      success: true,
      hasEmbeddedData,
      data: parsedData,
      savedFile: `/uploads/pdfs/${fileName}`,
      fileName: fileName,
      fileSize: file.size
    });

  } catch (error) {
    console.error('Error parsing PDF:', error);
    return NextResponse.json(
      { error: 'Failed to parse PDF file' },
      { status: 500 }
    );
  }
}
