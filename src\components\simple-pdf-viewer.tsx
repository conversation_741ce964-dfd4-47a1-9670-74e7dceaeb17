"use client";

import { useState, useEffect } from "react";
import { AlertCircle, Loader2, Download, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SimplePDFViewerProps {
  pdfUrl: string;
  fileName?: string;
  className?: string;
}

export function SimplePDFViewer({ pdfUrl, fileName, className }: SimplePDFViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfExists, setPdfExists] = useState(false);

  const validatePdf = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(pdfUrl, { method: "HEAD" });
      if (!response.ok) {
        throw new Error(`PDF not found: ${response.status} ${response.statusText}`);
      }

      setPdfExists(true);
    } catch (err) {
      console.error('Error validating PDF:', err);
      setError(err instanceof Error ? err.message : 'Failed to load PDF');
      setPdfExists(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    validatePdf();
  }, [pdfUrl]);

  const handleDownload = async () => {
    try {
      const response = await fetch(pdfUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading PDF:', err);
    }
  };

  const handleOpenInNewTab = () => {
    window.open(pdfUrl, '_blank');
  };

  const handleRetry = () => {
    validatePdf();
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-full bg-background ${className}`}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-sm text-muted-foreground">Loading PDF...</p>
        </div>
      </div>
    );
  }

  if (error || !pdfExists) {
    return (
      <div className={`flex items-center justify-center h-full bg-background ${className}`}>
        <div className="text-center max-w-md">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2 text-destructive">
            PDF Loading Error
          </h3>
          <p className="text-muted-foreground text-sm mb-4">
            {error || 'PDF file could not be loaded'}
          </p>
          <div className="text-xs text-muted-foreground bg-muted p-3 rounded mb-4">
            <p><strong>PDF URL:</strong> {pdfUrl}</p>
            {fileName && <p><strong>File Name:</strong> {fileName}</p>}
          </div>
          <div className="flex gap-2 justify-center">
            <Button variant="outline" size="sm" onClick={handleRetry}>
              Retry Loading
            </Button>
            <Button variant="outline" size="sm" onClick={handleOpenInNewTab}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Open in New Tab
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/30">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">
            {fileName || 'PDF Document'}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleOpenInNewTab}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Open in New Tab
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </div>

      {/* PDF Display */}
      <div className="flex-1 p-4">
        <div className="h-full border rounded-lg overflow-hidden bg-white">
          <iframe
            src={pdfUrl}
            className="w-full h-full border-0"
            title="PDF Preview"
            style={{ minHeight: '600px' }}
          />
        </div>
      </div>
    </div>
  );
}
